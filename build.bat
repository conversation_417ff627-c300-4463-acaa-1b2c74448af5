@echo off
REM 随时问系统 鉴权服务 Docker 构建脚本 (Windows)
REM 版本: v0.4

echo 🚀 开始构建 随时问系统 鉴权服务 Docker 镜像...

REM 检查 Docker 是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 服务未运行，请先启动 Docker Desktop
    pause
    exit /b 1
)

REM 构建并推送镜像
echo 📦 构建镜像: registry.cn-hangzhou.aliyuncs.com/weisoft/weisoftauth:v0.4

docker buildx build ^
  --platform linux/amd64 ^
  -t registry.cn-hangzhou.aliyuncs.com/weisoft/weisoftauth:v0.4 ^
  --build-arg NODE_MIRROR=registry.npmmirror.com ^
  --push ^
  .

if %errorlevel% equ 0 (
    echo ✅ 镜像构建并推送成功！
    echo 🎉 镜像地址: registry.cn-hangzhou.aliyuncs.com/weisoft/weisoftauth:v0.4
) else (
    echo ❌ 镜像构建失败
    pause
    exit /b 1
)

pause
