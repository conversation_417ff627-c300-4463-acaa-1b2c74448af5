# 使用官方Node.js基础镜像
FROM registry.cn-shanghai.aliyuncs.com/weisoft-images/node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 使用淘宝镜像加速 npm 安装

RUN npm config set registry https://registry.npmmirror.com && \
    npm install --production

# 复制应用代码
COPY . .

# 暴露应用端口
EXPOSE 3008

# 启动应用
CMD ["node", "/app/node_modules/pm2/bin/pm2-runtime", "start", "ecosystem.config.js"]

