// 引入 dotenv 模块（必须放在代码最顶部，确保优先加载环境变量）
const dotenv = require('dotenv');
// 明确加载 .env.production 文件
const result = dotenv.config({ path: '.env.production' });

// 增加错误处理，验证文件是否加载成功
if (result.error) {
  console.error('❌ 加载 .env.production 失败:', result.error.message);
} else {
  console.log('✅ 成功加载 .env.production 文件');
}

const express = require('express');
const jwt = require('jsonwebtoken');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

const app = express();
const port = process.env.PORT || 3008;

// 解析 JSON 请求体
app.use(express.json());

// 安全HTTP头中间件
app.use(helmet());

// 验证环境变量
const checkEnv = () => {
  const requiredEnv = ['JWT_SECRET', 'INTEGRATOR_SECRET'];
  requiredEnv.forEach(env => {
    if (!process.env[env]) {
      console.error(`[致命错误] 缺少环境变量 ${env}，服务启动失败！`);
      process.exit(1);
    }
    if (process.env[env].length < 32) {
      console.error(`[致命错误] 环境变量 ${env} 长度过短，服务启动失败！`);
      process.exit(1);
    }
    console.log(`[环境变量] ${env}: ${process.env[env].substring(0, 10)}...`);
  });
};
checkEnv();

const { JWT_SECRET, INTEGRATOR_SECRET } = process.env;

// JWT格式验证正则
const JWT_REGEX = /^[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*$/;

// 允许的过期时间范围
const ALLOWED_EXPIRES_IN = ['1h', '2h', '6h', '12h', '24h'];

// 接口速率限制
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每IP每15分钟最多100次请求
  message: '请求过于频繁，请稍后再试'
});

// 对所有/auth/开头的接口应用速率限制
app.use('/shareAuth', authLimiter);

/**
 * 1. 生成分享 token 接口
 */
app.post('/genshareToken', (req, res) => {
  try {
    const { token: integratorToken, expiresIn = '24h' } = req.body;
    
    // 输入验证
    if (!integratorToken) {
      return res.status(400).json({ success: false, message: '缺少集成方身份 token' });
    }
    
    if (!JWT_REGEX.test(integratorToken)) {
      return res.status(400).json({ success: false, message: '无效的 JWT 格式' });
    }
    
    if (integratorToken.length > 1000) {
      return res.status(400).json({ success: false, message: '集成方身份 token 长度过长' });
    }
    
    if (!ALLOWED_EXPIRES_IN.includes(expiresIn)) {
      return res.status(400).json({ success: false, message: '不允许的过期时间设置' });
    }
    
    // 验证集成方 token（强制使用 HS256 算法）
    let decoded;
    try {
      decoded = jwt.verify(integratorToken, INTEGRATOR_SECRET, { algorithms: ['HS256'] });
    } catch (err) {
      return res.status(401).json({ success: false, message: '集成方身份 token 无效' });
    }
    
    // 提取并验证 userId
    const { userId } = decoded;
    if (!userId) {
      return res.status(400).json({ success: false, message: '集成方 token 中缺少 userId 字段' });
    }
    
    // 生成分享 token（强制使用 HS256 算法）
    const shareToken = jwt.sign(
      { userId },
      JWT_SECRET,
      { expiresIn, algorithm: 'HS256' }
    );
    
    res.json({
      success: true,
      data: {
        shareToken,
        uid: userId
      }
    });
  } catch (error) {
    console.error('[genshareToken] 接口异常:', error);
    res.status(500).json({ success: false, message: '生成分享 token 失败' });
  }
});

/**
 * 2. 随时问平台 初始化验证接口
 */
app.post('/shareAuth/init', (req, res) => {
  try {
    const { token: shareToken } = req.body;
    
    // 输入验证
    if (!shareToken) {
      return res.status(400).json({ success: false, message: '缺少分享 token' });
    }
    
    if (!JWT_REGEX.test(shareToken)) {
      return res.status(400).json({ success: false, message: '无效的 JWT 格式' });
    }
    
    if (shareToken.length > 1000) {
      return res.status(400).json({ success: false, message: '分享 token 长度过长' });
    }
    
    // 验证分享 token（强制使用 HS256 算法）
    let decoded;
    try {
      decoded = jwt.verify(shareToken, JWT_SECRET, { algorithms: ['HS256'] });
    } catch (err) {
      return res.status(401).json({ success: false, message: '分享 token 无效' });
    }
    
    res.json({
      success: true,
      data: { uid: decoded.userId }
    });
  } catch (error) {
    console.error('[init] 接口异常:', error);
    res.status(500).json({ success: false, message: '初始化验证失败' });
  }
});

/**
 * 3. 随时问平台 对话验证接口
 */
app.post('/shareAuth/start', (req, res) => {
  try {
    const { token: shareToken, question } = req.body;
    
    // 输入验证
    if (!shareToken) {
      return res.status(400).json({ success: false, message: '缺少分享 token' });
    }
    
    if (!JWT_REGEX.test(shareToken)) {
      return res.status(400).json({ success: false, message: '无效的 JWT 格式' });
    }
    
    if (shareToken.length > 1000) {
      return res.status(400).json({ success: false, message: '分享 token 长度过长' });
    }
    
    if (question && question.length > 1000) {
      return res.status(400).json({ success: false, message: '问题长度超过限制' });
    }
    
    // 验证分享 token（强制使用 HS256 算法）
    let decoded;
    try {
      decoded = jwt.verify(shareToken, JWT_SECRET, { algorithms: ['HS256'] });
    } catch (err) {
      return res.status(401).json({ success: false, message: '分享 token 无效' });
    }
    
    res.json({
      success: true,
      data: { uid: decoded.userId }
    });
  } catch (error) {
    console.error('[start] 接口异常:', error);
    res.status(500).json({ success: false, message: '对话验证失败' });
  }
});

// 统一错误处理中间件
app.use((err, req, res, next) => {
  console.error('[系统错误]', err);
  res.status(500).json({
    success: false,
    message: '服务器内部错误'
  });
});

// 启动服务
app.listen(port, '0.0.0.0', () => {
  console.log(`服务器启动成功，端口: ${port}`);
  console.log('可用接口：');
  console.log('- 集成方生成分享 token：POST /genshareToken');
  console.log('- 随时问平台 初始化验证：POST /shareAuth/init');
  console.log('- 随时问平台 对话验证：POST /shareAuth/start');
});
