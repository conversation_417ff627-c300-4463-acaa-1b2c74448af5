// 引入 dotenv 模块（必须放在代码最顶部，确保优先加载环境变量）
const dotenv = require('dotenv');
// 明确加载 .env.production 文件
const result = dotenv.config({ path: '.env.production' });

// 增加错误处理，验证文件是否加载成功
if (result.error) {
  console.error('❌ 加载 .env.production 失败:', result.error.message);
} else {
  console.log('✅ 成功加载 .env.production 文件');
}

const express = require('express');
const jwt = require('jsonwebtoken');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const fs = require('fs');
const path = require('path');

const app = express();
const port = process.env.PORT || 3008;

// 日志记录功能
const logDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 获取客户端真实IP地址
const getClientIP = (req) => {
  return req.headers['x-forwarded-for'] ||
         req.headers['x-real-ip'] ||
         req.connection.remoteAddress ||
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
         req.ip;
};

// 记录访问日志
const logAccess = (req, endpoint, status, message, additionalInfo = {}) => {
  const timestamp = new Date().toISOString();
  const clientIP = getClientIP(req);
  const userAgent = req.headers['user-agent'] || 'Unknown';
  const referer = req.headers['referer'] || 'Direct';

  const logEntry = {
    timestamp,
    endpoint,
    method: req.method,
    clientIP,
    userAgent,
    referer,
    status,
    message,
    requestBody: req.body ? JSON.stringify(req.body).substring(0, 200) : 'Empty',
    ...additionalInfo
  };

  // 控制台输出
  console.log(`[${timestamp}] ${endpoint} - IP: ${clientIP} - Status: ${status} - ${message}`);

  // 写入日志文件
  const logFile = path.join(logDir, `access-${new Date().toISOString().split('T')[0]}.log`);
  fs.appendFileSync(logFile, JSON.stringify(logEntry) + '\n');
};

// 请求统计
const requestStats = {
  total: 0,
  byIP: new Map(),
  byEndpoint: new Map(),
  errors: 0
};

// 统计中间件
const statsMiddleware = (req, res, next) => {
  const clientIP = getClientIP(req);
  const endpoint = req.path;

  requestStats.total++;
  requestStats.byIP.set(clientIP, (requestStats.byIP.get(clientIP) || 0) + 1);
  requestStats.byEndpoint.set(endpoint, (requestStats.byEndpoint.get(endpoint) || 0) + 1);

  next();
};

// 解析 JSON 请求体
app.use(express.json());

// 安全HTTP头中间件
app.use(helmet());

// 应用统计中间件
app.use(statsMiddleware);

// 验证环境变量
const checkEnv = () => {
  const requiredEnv = ['JWT_SECRET', 'INTEGRATOR_SECRET'];
  requiredEnv.forEach(env => {
    if (!process.env[env]) {
      console.error(`[致命错误] 缺少环境变量 ${env}，服务启动失败！`);
      process.exit(1);
    }
    if (process.env[env].length < 32) {
      console.error(`[致命错误] 环境变量 ${env} 长度过短，服务启动失败！`);
      process.exit(1);
    }
    console.log(`[环境变量] ${env}: ${process.env[env].substring(0, 10)}...`);
  });
};
checkEnv();

const { JWT_SECRET, INTEGRATOR_SECRET } = process.env;

// JWT格式验证正则
const JWT_REGEX = /^[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*$/;

// 允许的过期时间范围
const ALLOWED_EXPIRES_IN = ['1h', '2h', '6h', '12h', '24h'];

// 更灵活的速率限制策略
const createRateLimiter = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: { success: false, message },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      const clientIP = getClientIP(req);
      logAccess(req, req.path, 429, `Rate limit exceeded: ${message}`, {
        rateLimitInfo: { windowMs, max },
        clientIP
      });
      requestStats.errors++;
      res.status(429).json({ success: false, message });
    }
  });
};

// 不同接口使用不同的速率限制
const initLimiter = createRateLimiter(
  5 * 60 * 1000,  // 5分钟
  200,            // 每5分钟200次请求
  '初始化请求过于频繁，请稍后再试'
);

const startLimiter = createRateLimiter(
  1 * 60 * 1000,  // 1分钟
  60,             // 每分钟60次请求
  '对话请求过于频繁，请稍后再试'
);

const genTokenLimiter = createRateLimiter(
  15 * 60 * 1000, // 15分钟
  50,             // 每15分钟50次请求
  '生成token请求过于频繁，请稍后再试'
);

// 应用速率限制到特定接口
app.use('/shareAuth/init', initLimiter);
app.use('/shareAuth/start', startLimiter);
app.use('/genshareToken', genTokenLimiter);

/**
 * 1. 生成分享 token 接口
 */
app.post('/genshareToken', (req, res) => {
  const startTime = Date.now();
  const clientIP = getClientIP(req);

  try {
    const { token: integratorToken, expiresIn = '24h' } = req.body;

    logAccess(req, '/genshareToken', 'REQUEST', '开始处理生成token请求', {
      clientIP,
      expiresIn,
      tokenLength: integratorToken ? integratorToken.length : 0
    });

    // 输入验证
    if (!integratorToken) {
      logAccess(req, '/genshareToken', 400, '缺少集成方身份token', { clientIP });
      return res.status(400).json({ success: false, message: '缺少集成方身份 token' });
    }

    if (!JWT_REGEX.test(integratorToken)) {
      logAccess(req, '/genshareToken', 400, '无效的JWT格式', { clientIP });
      return res.status(400).json({ success: false, message: '无效的 JWT 格式' });
    }

    if (integratorToken.length > 1000) {
      logAccess(req, '/genshareToken', 400, 'Token长度过长', { clientIP, tokenLength: integratorToken.length });
      return res.status(400).json({ success: false, message: '集成方身份 token 长度过长' });
    }

    if (!ALLOWED_EXPIRES_IN.includes(expiresIn)) {
      logAccess(req, '/genshareToken', 400, '不允许的过期时间', { clientIP, expiresIn });
      return res.status(400).json({ success: false, message: '不允许的过期时间设置' });
    }

    // 验证集成方 token（强制使用 HS256 算法）
    let decoded;
    try {
      decoded = jwt.verify(integratorToken, INTEGRATOR_SECRET, { algorithms: ['HS256'] });
    } catch (err) {
      logAccess(req, '/genshareToken', 401, '集成方token验证失败', {
        clientIP,
        error: err.message,
        tokenPrefix: integratorToken.substring(0, 20) + '...'
      });
      requestStats.errors++;
      return res.status(401).json({ success: false, message: '集成方身份 token 无效' });
    }

    // 提取并验证 userId
    const { userId } = decoded;
    if (!userId) {
      logAccess(req, '/genshareToken', 400, 'Token中缺少userId', { clientIP });
      return res.status(400).json({ success: false, message: '集成方 token 中缺少 userId 字段' });
    }

    // 生成分享 token（强制使用 HS256 算法）
    const shareToken = jwt.sign(
      { userId },
      JWT_SECRET,
      { expiresIn, algorithm: 'HS256' }
    );

    const responseTime = Date.now() - startTime;
    logAccess(req, '/genshareToken', 200, '成功生成分享token', {
      clientIP,
      userId,
      expiresIn,
      responseTime: `${responseTime}ms`,
      shareTokenLength: shareToken.length
    });

    res.json({
      success: true,
      data: {
        shareToken,
        uid: userId
      }
    });
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logAccess(req, '/genshareToken', 500, '接口异常', {
      clientIP,
      error: error.message,
      stack: error.stack,
      responseTime: `${responseTime}ms`
    });
    requestStats.errors++;
    console.error('[genshareToken] 接口异常:', error);
    res.status(500).json({ success: false, message: '生成分享 token 失败' });
  }
});

/**
 * 2. 随时问平台 初始化验证接口
 */
app.post('/shareAuth/init', (req, res) => {
  const startTime = Date.now();
  const clientIP = getClientIP(req);

  try {
    const { token: shareToken } = req.body;

    logAccess(req, '/shareAuth/init', 'REQUEST', '开始处理初始化验证请求', {
      clientIP,
      tokenLength: shareToken ? shareToken.length : 0
    });

    // 输入验证
    if (!shareToken) {
      logAccess(req, '/shareAuth/init', 400, '缺少分享token', { clientIP });
      return res.status(400).json({ success: false, message: '缺少分享 token' });
    }

    if (!JWT_REGEX.test(shareToken)) {
      logAccess(req, '/shareAuth/init', 400, '无效的JWT格式', { clientIP });
      return res.status(400).json({ success: false, message: '无效的 JWT 格式' });
    }

    if (shareToken.length > 1000) {
      logAccess(req, '/shareAuth/init', 400, 'Token长度过长', {
        clientIP,
        tokenLength: shareToken.length
      });
      return res.status(400).json({ success: false, message: '分享 token 长度过长' });
    }

    // 验证分享 token（强制使用 HS256 算法）
    let decoded;
    try {
      decoded = jwt.verify(shareToken, JWT_SECRET, { algorithms: ['HS256'] });
    } catch (err) {
      logAccess(req, '/shareAuth/init', 401, '分享token验证失败', {
        clientIP,
        error: err.message,
        tokenPrefix: shareToken.substring(0, 20) + '...'
      });
      requestStats.errors++;
      return res.status(401).json({ success: false, message: '分享 token 无效' });
    }

    const responseTime = Date.now() - startTime;
    logAccess(req, '/shareAuth/init', 200, '初始化验证成功', {
      clientIP,
      userId: decoded.userId,
      responseTime: `${responseTime}ms`
    });

    res.json({
      success: true,
      data: { uid: decoded.userId }
    });
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logAccess(req, '/shareAuth/init', 500, '接口异常', {
      clientIP,
      error: error.message,
      stack: error.stack,
      responseTime: `${responseTime}ms`
    });
    requestStats.errors++;
    console.error('[init] 接口异常:', error);
    res.status(500).json({ success: false, message: '初始化验证失败' });
  }
});

/**
 * 3. 随时问平台 对话验证接口
 */
app.post('/shareAuth/start', (req, res) => {
  const startTime = Date.now();
  const clientIP = getClientIP(req);

  try {
    const { token: shareToken, question } = req.body;

    logAccess(req, '/shareAuth/start', 'REQUEST', '开始处理对话验证请求', {
      clientIP,
      tokenLength: shareToken ? shareToken.length : 0,
      questionLength: question ? question.length : 0
    });

    // 输入验证
    if (!shareToken) {
      logAccess(req, '/shareAuth/start', 400, '缺少分享token', { clientIP });
      return res.status(400).json({ success: false, message: '缺少分享 token' });
    }

    if (!JWT_REGEX.test(shareToken)) {
      logAccess(req, '/shareAuth/start', 400, '无效的JWT格式', { clientIP });
      return res.status(400).json({ success: false, message: '无效的 JWT 格式' });
    }

    if (shareToken.length > 1000) {
      logAccess(req, '/shareAuth/start', 400, 'Token长度过长', {
        clientIP,
        tokenLength: shareToken.length
      });
      return res.status(400).json({ success: false, message: '分享 token 长度过长' });
    }

    if (question && question.length > 1000) {
      logAccess(req, '/shareAuth/start', 400, '问题长度超过限制', {
        clientIP,
        questionLength: question.length
      });
      return res.status(400).json({ success: false, message: '问题长度超过限制' });
    }

    // 验证分享 token（强制使用 HS256 算法）
    let decoded;
    try {
      decoded = jwt.verify(shareToken, JWT_SECRET, { algorithms: ['HS256'] });
    } catch (err) {
      logAccess(req, '/shareAuth/start', 401, '分享token验证失败', {
        clientIP,
        error: err.message,
        tokenPrefix: shareToken.substring(0, 20) + '...'
      });
      requestStats.errors++;
      return res.status(401).json({ success: false, message: '分享 token 无效' });
    }

    const responseTime = Date.now() - startTime;
    logAccess(req, '/shareAuth/start', 200, '对话验证成功', {
      clientIP,
      userId: decoded.userId,
      questionLength: question ? question.length : 0,
      responseTime: `${responseTime}ms`
    });

    res.json({
      success: true,
      data: { uid: decoded.userId }
    });
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logAccess(req, '/shareAuth/start', 500, '接口异常', {
      clientIP,
      error: error.message,
      stack: error.stack,
      responseTime: `${responseTime}ms`
    });
    requestStats.errors++;
    console.error('[start] 接口异常:', error);
    res.status(500).json({ success: false, message: '对话验证失败' });
  }
});

// 统计信息接口
app.get('/stats', (req, res) => {
  const clientIP = getClientIP(req);

  logAccess(req, '/stats', 'REQUEST', '查询统计信息', { clientIP });

  const topIPs = Array.from(requestStats.byIP.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([ip, count]) => ({ ip, count }));

  const topEndpoints = Array.from(requestStats.byEndpoint.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([endpoint, count]) => ({ endpoint, count }));

  const stats = {
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    totalRequests: requestStats.total,
    totalErrors: requestStats.errors,
    errorRate: requestStats.total > 0 ? (requestStats.errors / requestStats.total * 100).toFixed(2) + '%' : '0%',
    topIPs,
    topEndpoints,
    memoryUsage: process.memoryUsage(),
    nodeVersion: process.version
  };

  logAccess(req, '/stats', 200, '统计信息查询成功', {
    clientIP,
    totalRequests: requestStats.total,
    totalErrors: requestStats.errors
  });

  res.json({
    success: true,
    data: stats
  });
});

// 健康检查接口
app.get('/health', (req, res) => {
  const clientIP = getClientIP(req);

  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0'
  };

  logAccess(req, '/health', 200, '健康检查', { clientIP });

  res.json(health);
});

// 统一错误处理中间件
app.use((err, req, res, next) => {
  const clientIP = getClientIP(req);

  logAccess(req, req.path || 'unknown', 500, '系统错误', {
    clientIP,
    error: err.message,
    stack: err.stack
  });

  requestStats.errors++;
  console.error('[系统错误]', err);

  res.status(500).json({
    success: false,
    message: '服务器内部错误'
  });
});

// 404处理
app.use('*', (req, res) => {
  const clientIP = getClientIP(req);

  logAccess(req, req.path, 404, '接口不存在', { clientIP });

  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// 启动服务
app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 服务器启动成功，端口: ${port}`);
  console.log('📊 可用接口：');
  console.log('- 集成方生成分享 token：POST /genshareToken');
  console.log('- 随时问平台 初始化验证：POST /shareAuth/init');
  console.log('- 随时问平台 对话验证：POST /shareAuth/start');
  console.log('- 统计信息查询：GET /stats');
  console.log('- 健康检查：GET /health');
  console.log('📁 日志文件保存在: ./logs/');

  // 定期输出统计信息
  setInterval(() => {
    console.log(`📈 [统计] 总请求: ${requestStats.total}, 错误: ${requestStats.errors}, 错误率: ${requestStats.total > 0 ? (requestStats.errors / requestStats.total * 100).toFixed(2) : 0}%`);
  }, 5 * 60 * 1000); // 每5分钟输出一次
});
