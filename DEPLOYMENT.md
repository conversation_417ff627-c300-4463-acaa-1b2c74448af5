# 部署指南

## 问题分析

### "Too Many Requests" 错误原因

1. **速率限制过于严格**: 原来的设置每IP每15分钟只允许100次请求
2. **随时问系统频繁调用**: 分享链接在初始化和每次对话时都会调用鉴权接口
3. **缺少监控**: 无法了解实际的请求模式和频率

### 解决方案

1. **优化速率限制策略**:
   - 初始化接口: 5分钟200次 (更宽松)
   - 对话接口: 1分钟60次 (适中)
   - 生成Token: 15分钟50次 (较严格)

2. **增加详细日志**:
   - 记录每个请求的IP、用户信息、响应时间
   - 按日期分文件存储
   - 实时统计和监控

3. **添加监控接口**:
   - `/stats` - 查看请求统计
   - `/health` - 健康检查

## 部署步骤

### 1. 启动 Docker Desktop

确保 Docker Desktop 正在运行，然后执行构建命令。

### 2. 构建镜像

#### Windows 用户:
```bash
build.bat
```

#### Linux/Mac 用户:
```bash
chmod +x build.sh
./build.sh
```

#### 手动构建:
```bash
docker buildx build \
  --platform linux/amd64 \
  -t registry.cn-hangzhou.aliyuncs.com/weisoft/weisoftauth:v0.4 \
  --build-arg NODE_MIRROR=registry.npmmirror.com \
  --push \
  .
```

### 3. 更新部署

将您的容器镜像版本从 `v0.3` 更新到 `v0.4`:

```bash
# 停止旧容器
docker stop your-container-name

# 拉取新镜像
docker pull registry.cn-hangzhou.aliyuncs.com/weisoft/weisoftauth:v0.4

# 启动新容器
docker run -d \
  --name your-container-name \
  -p 3008:3008 \
  -e JWT_SECRET=your_jwt_secret \
  -e INTEGRATOR_SECRET=your_integrator_secret \
  registry.cn-hangzhou.aliyuncs.com/weisoft/weisoftauth:v0.4
```

### 4. 验证部署

#### 检查服务状态:
```bash
curl http://your-server:3008/health
```

#### 查看统计信息:
```bash
curl http://your-server:3008/stats
```

#### 运行完整测试:
```bash
npm install node-fetch
npm test
```

## 监控和维护

### 1. 日志查看

#### 访问日志:
```bash
# 查看今天的访问日志
tail -f logs/access-$(date +%Y-%m-%d).log

# 查看特定日期的日志
cat logs/access-2024-01-15.log
```

#### 容器日志:
```bash
docker logs your-container-name
```

### 2. 性能监控

#### 实时统计:
- 访问 `http://your-server:3008/stats` 查看详细统计
- 包含请求总数、错误率、Top IP等信息

#### 关键指标:
- **总请求数**: 服务启动以来的总请求量
- **错误率**: 错误请求占总请求的百分比
- **Top IP**: 请求最频繁的IP地址
- **响应时间**: 平均响应时间

### 3. 故障排查

#### 高频请求分析:
```bash
# 查看今天访问最频繁的IP
grep $(date +%Y-%m-%d) logs/access-*.log | \
  jq -r '.clientIP' | sort | uniq -c | sort -nr | head -10
```

#### 错误请求分析:
```bash
# 查看今天的错误请求
grep $(date +%Y-%m-%d) logs/access-*.log | \
  jq 'select(.status >= 400)'
```

#### 速率限制触发分析:
```bash
# 查看被速率限制的请求
grep "Rate limit exceeded" logs/access-*.log
```

## 配置调优

### 根据实际情况调整速率限制

如果仍然出现 "Too Many Requests" 错误，可以进一步调整速率限制参数:

```javascript
// 在 server.js 中修改这些值
const initLimiter = createRateLimiter(
  5 * 60 * 1000,  // 时间窗口 (毫秒)
  300,            // 最大请求数 (增加到300)
  '初始化请求过于频繁，请稍后再试'
);

const startLimiter = createRateLimiter(
  1 * 60 * 1000,  // 时间窗口
  100,            // 最大请求数 (增加到100)
  '对话请求过于频繁，请稍后再试'
);
```

### 环境变量配置

```bash
# 基础配置
JWT_SECRET=your_very_long_jwt_secret_at_least_32_characters
INTEGRATOR_SECRET=your_very_long_integrator_secret_at_least_32_characters
PORT=3008

# 可选配置
NODE_ENV=production
LOG_LEVEL=info
```

## 版本更新记录

### v0.4 改进内容:
- ✅ 优化速率限制策略，减少误拦截
- ✅ 添加详细的访问日志记录
- ✅ 增加实时统计监控功能
- ✅ 改进错误处理和响应
- ✅ 添加健康检查和统计接口
- ✅ 提供完整的测试和部署脚本

### 预期效果:
- 大幅减少 "Too Many Requests" 错误
- 提供详细的访问分析能力
- 更好的服务监控和故障排查
- 更稳定的服务运行
