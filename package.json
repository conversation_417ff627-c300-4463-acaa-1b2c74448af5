{"name": "auth-service", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node server.js", "dev": "node server.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:logs": "pm2 logs", "logs:access": "tail -f logs/access-$(date +%Y-%m-%d).log", "logs:clear": "rm -f logs/*.log", "test": "node test-api.js", "build": "chmod +x build.sh && ./build.sh", "build:win": "build.bat"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"dotenv": "^16.5.0", "express": "^4.19.2", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "pm2": "^5.3.1"}, "devDependencies": {"node-fetch": "^2.7.0"}}