const jwt = require('jsonwebtoken');

// 集成方自行定义的密钥（需与服务端保持一致）
const INTEGRATOR_SECRET = 'unicom-weisfot-integrator-secret-key';

// 生成集成方 Token（包含用户信息）
function generateIntegratorToken(userId) {
  return jwt.sign(
    { userId },
    INTEGRATOR_SECRET,
    { 
      algorithm: 'HS256', // 显式指定使用 HS256 算法
      expiresIn: '2h'     // 建议较短有效期
    }
  );
}

// 示例：为用户 "integrator_user_1234" 生成 Token
const token = generateIntegratorToken('integrator_user_1234');
console.log('集成方 Token:', token);  
