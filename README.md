# 随时问系统 分享链接鉴权服务

基于随时问系统官方文档的分享链接鉴权服务，提供安全的 JWT token 验证功能。

## 功能特性

- 🔐 JWT token 生成和验证
- 🛡️ 多层级速率限制保护
- 📊 详细的访问日志记录
- 📈 实时统计监控
- 🚀 Docker 容器化部署
- 💾 PM2 进程管理

## API 接口

### 1. 生成分享 Token
```
POST /genshareToken
```

### 2. 初始化验证
```
POST /shareAuth/init
```

### 3. 对话验证
```
POST /shareAuth/start
```

### 4. 统计信息
```
GET /stats
```

### 5. 健康检查
```
GET /health
```

## 速率限制策略

- **初始化接口**: 每5分钟200次请求
- **对话接口**: 每分钟60次请求  
- **生成Token接口**: 每15分钟50次请求

## 日志功能

### 访问日志
- 记录所有API请求的详细信息
- 包含IP地址、用户代理、响应时间等
- 按日期分文件存储在 `logs/` 目录

### 统计监控
- 实时请求统计
- 错误率监控
- Top IP和接口访问排行
- 内存使用情况

## 环境变量

```bash
JWT_SECRET=your_jwt_secret_here
INTEGRATOR_SECRET=your_integrator_secret_here
PORT=3008
```

## 快速启动

### 开发模式
```bash
npm run dev
```

### 生产模式
```bash
npm run pm2:start
```

### Docker 部署
```bash
docker buildx build \
  --platform linux/amd64 \
  -t registry.cn-hangzhou.aliyuncs.com/weisoft/weisoftauth:v0.4 \
  --build-arg NODE_MIRROR=registry.npmmirror.com \
  --push \
  .
```

## 日志管理

### 查看实时访问日志
```bash
npm run logs:access
```

### 查看PM2日志
```bash
npm run pm2:logs
```

### 清理日志文件
```bash
npm run logs:clear
```

## 问题排查

### "Too Many Requests" 错误
1. 检查 `/stats` 接口查看请求统计
2. 查看访问日志分析请求模式
3. 根据实际需求调整速率限制参数

### 性能监控
- 通过 `/health` 接口检查服务状态
- 通过 `/stats` 接口查看详细统计信息
- 查看日志文件分析请求趋势

## 版本历史

### v0.4 (当前版本)
- ✅ 优化速率限制策略
- ✅ 添加详细访问日志
- ✅ 增加统计监控功能
- ✅ 改进错误处理
- ✅ 添加健康检查接口

### v0.3
- 基础鉴权功能
- 简单速率限制
