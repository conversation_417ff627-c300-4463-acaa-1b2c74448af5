// 随时问系统 鉴权服务 API 测试脚本
const jwt = require('jsonwebtoken');

// 配置
const BASE_URL = 'http://localhost:3008';
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_here';
const INTEGRATOR_SECRET = process.env.INTEGRATOR_SECRET || 'your_integrator_secret_here';

// 测试用户ID
const TEST_USER_ID = 'test_user_12345';

// HTTP 请求函数
async function makeRequest(url, method = 'GET', body = null) {
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'SuiShiWen-Auth-Test/1.0'
    }
  };
  
  if (body) {
    options.body = JSON.stringify(body);
  }
  
  try {
    const response = await fetch(url, options);
    const data = await response.json();
    return { status: response.status, data };
  } catch (error) {
    return { status: 0, error: error.message };
  }
}

// 测试函数
async function testHealthCheck() {
  console.log('\n🔍 测试健康检查接口...');
  const result = await makeRequest(`${BASE_URL}/health`);
  console.log('状态码:', result.status);
  console.log('响应:', JSON.stringify(result.data, null, 2));
  return result.status === 200;
}

async function testGenerateToken() {
  console.log('\n🔑 测试生成分享Token接口...');
  
  // 生成集成方token
  const integratorToken = jwt.sign(
    { userId: TEST_USER_ID },
    INTEGRATOR_SECRET,
    { expiresIn: '1h', algorithm: 'HS256' }
  );
  
  const result = await makeRequest(`${BASE_URL}/genshareToken`, 'POST', {
    token: integratorToken,
    expiresIn: '24h'
  });
  
  console.log('状态码:', result.status);
  console.log('响应:', JSON.stringify(result.data, null, 2));
  
  if (result.status === 200 && result.data.success) {
    return result.data.data.shareToken;
  }
  return null;
}

async function testInitAuth(shareToken) {
  console.log('\n🚀 测试初始化验证接口...');
  
  const result = await makeRequest(`${BASE_URL}/shareAuth/init`, 'POST', {
    token: shareToken
  });
  
  console.log('状态码:', result.status);
  console.log('响应:', JSON.stringify(result.data, null, 2));
  return result.status === 200;
}

async function testStartAuth(shareToken) {
  console.log('\n💬 测试对话验证接口...');
  
  const result = await makeRequest(`${BASE_URL}/shareAuth/start`, 'POST', {
    token: shareToken,
    question: '这是一个测试问题'
  });
  
  console.log('状态码:', result.status);
  console.log('响应:', JSON.stringify(result.data, null, 2));
  return result.status === 200;
}

async function testStats() {
  console.log('\n📊 测试统计信息接口...');
  
  const result = await makeRequest(`${BASE_URL}/stats`);
  console.log('状态码:', result.status);
  console.log('响应:', JSON.stringify(result.data, null, 2));
  return result.status === 200;
}

async function testRateLimit() {
  console.log('\n⚡ 测试速率限制...');
  
  // 快速发送多个请求测试速率限制
  const promises = [];
  for (let i = 0; i < 5; i++) {
    promises.push(makeRequest(`${BASE_URL}/health`));
  }
  
  const results = await Promise.all(promises);
  const successCount = results.filter(r => r.status === 200).length;
  const rateLimitCount = results.filter(r => r.status === 429).length;
  
  console.log(`成功请求: ${successCount}, 被限制请求: ${rateLimitCount}`);
  return true;
}

// 主测试函数
async function runTests() {
  console.log('🧪 开始测试 随时问系统 鉴权服务...');
  console.log('服务地址:', BASE_URL);
  
  const tests = [
    { name: '健康检查', fn: testHealthCheck },
    { name: '生成Token', fn: testGenerateToken },
    { name: '速率限制', fn: testRateLimit },
    { name: '统计信息', fn: testStats }
  ];
  
  let shareToken = null;
  let passedTests = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (test.name === '生成Token' && result) {
        shareToken = result;
      }
      if (result) {
        console.log(`✅ ${test.name} 测试通过`);
        passedTests++;
      } else {
        console.log(`❌ ${test.name} 测试失败`);
      }
    } catch (error) {
      console.log(`❌ ${test.name} 测试异常:`, error.message);
    }
  }
  
  // 如果有shareToken，测试验证接口
  if (shareToken) {
    try {
      const initResult = await testInitAuth(shareToken);
      const startResult = await testStartAuth(shareToken);
      
      if (initResult) {
        console.log('✅ 初始化验证 测试通过');
        passedTests++;
      } else {
        console.log('❌ 初始化验证 测试失败');
      }
      
      if (startResult) {
        console.log('✅ 对话验证 测试通过');
        passedTests++;
      } else {
        console.log('❌ 对话验证 测试失败');
      }
    } catch (error) {
      console.log('❌ 验证接口测试异常:', error.message);
    }
  }
  
  console.log(`\n🎯 测试完成: ${passedTests}/${tests.length + (shareToken ? 2 : 0)} 通过`);
  
  if (passedTests === tests.length + (shareToken ? 2 : 0)) {
    console.log('🎉 所有测试通过！服务运行正常');
  } else {
    console.log('⚠️  部分测试失败，请检查服务配置');
  }
}

// 检查是否在Node.js环境中运行
if (typeof window === 'undefined') {
  // Node.js环境，需要安装node-fetch
  try {
    const fetch = require('node-fetch');
    global.fetch = fetch;
    runTests().catch(console.error);
  } catch (error) {
    console.error('❌ 请先安装 node-fetch: npm install node-fetch@2.7.0');
    console.error('错误详情:', error.message);
  }
} else {
  console.log('请在Node.js环境中运行此测试脚本');
}
