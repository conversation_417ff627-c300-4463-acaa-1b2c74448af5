#!/bin/bash

# 随时问系统 鉴权服务 Docker 构建脚本
# 版本: v0.4

echo "🚀 开始构建 随时问系统 鉴权服务 Docker 镜像..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 服务未运行，请先启动 Docker Desktop"
    exit 1
fi

# 构建并推送镜像
echo "📦 构建镜像: registry.cn-hangzhou.aliyuncs.com/weisoft/weisoftauth:v0.4"

docker buildx build \
  --platform linux/amd64 \
  -t registry.cn-hangzhou.aliyuncs.com/weisoft/weisoftauth:v0.4 \
  --build-arg NODE_MIRROR=registry.npmmirror.com \
  --push \
  .

if [ $? -eq 0 ]; then
    echo "✅ 镜像构建并推送成功！"
    echo "🎉 镜像地址: registry.cn-hangzhou.aliyuncs.com/weisoft/weisoftauth:v0.4"
else
    echo "❌ 镜像构建失败"
    exit 1
fi
